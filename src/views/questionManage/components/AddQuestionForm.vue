<!--
 * @Description: 添加题目
 * @Autor: Fhz
 * @Date: 2025-04-21 16:38:40
 * @LastEditors: panmy
 * @LastEditTime: 2025-07-31 18:29:49
-->

<template>
  <BasicModal v-bind="$attrs" class="transfer-modal member-modal" @register="registerModal" title="创建题目" showOkBtn @ok="handleSubmit">
    <BasicForm @register="registerForm">
      <template #send="{ model, field }">
        <mtcn-tree-select v-model="model.msmfSource" :options="dicOptions" placeholder="请选择数据字典" lastLevel allowClear />
      </template>
    </BasicForm>
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, toRefs, unref, computed, reactive, onMounted } from 'vue';
  import { BasicModal, useModalInner, useModal } from '@/components/Modal';
  import { BasicForm, useForm, FormSchema } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseStore } from '@/store/modules/base';
  import { addAloneField } from '@/api/serviceMatters/ItemFieldsConfig';
  import { getDictionaryTypeSelector } from '@/api/systemData/dictionary';

  const baseStore = useBaseStore();
  const id = ref('');
  const schemasForm: FormSchema[] = [
    {
      field: 'msmfName',
      label: '题目类型',
      component: 'Select',
      componentProps: {
        options: [
          { id: '1', fullName: '单选题' },
          { id: '2', fullName: '多选题' },
          { id: '3', fullName: '简答题' },
        ],
      },
      rules: [{ required: true, trigger: 'blur', message: '题目类型不能为空' }],
    },
    {
      field: 'msmfName',
      label: '题目',
      component: 'Textarea',
      componentProps: { placeholder: '输入题目名称' },
      rules: [{ required: true, trigger: 'blur', message: '题目名称不能为空' }],
    },

    // {
    //   field: 'msmfEname',
    //   label: '编码',
    //   component: 'Input',
    //   componentProps: { placeholder: '输入题目编码', maxlength: 10 },
    //   rules: [{ required: true, trigger: 'blur', message: '题目编码不能为空' }],
    // },

    {
      field: 'msmfType',
      label: '类型',
      component: 'Select',
      defaultValue: 'Input',
      componentProps: {
        allowClear: false,
        options: [
          { id: 'Input', fullName: '文本输入框Input' },
          { id: 'Radio', fullName: '单选Radio' },
          { id: 'Checkbox', fullName: '多选Checkbox' },
          { id: 'Select', fullName: '选择框Select' },
          { id: 'Textarea', fullName: '多行文本输入框Textarea' },
          { id: 'Editor', fullName: '富文本Editor' },
          { id: 'UploadImgSingle', fullName: '图片Image' },
          { id: 'UploadFile', fullName: '文件File' },
        ],
      },
      rules: [{ required: true, trigger: 'blur' }],
    },

    {
      field: 'msmfSource',
      label: '数据字典',
      component: 'TreeSelect',
      rules: [{ required: true, trigger: 'blur' }],
      ifShow: ({ values }) => {
        return ['Radio', 'Checkbox', 'Select'].includes(values.msmfType);
      },
    },
    {
      label: '是否必填',
      field: 'sybt',
      component: 'Switch',
      defaultValue: true,
      rules: [{ required: true, trigger: 'blur' }],
    },
    {
      label: '使用分值',
      field: 'syfz',
      component: 'Switch',
      defaultValue: true,
      rules: [{ required: true, trigger: 'blur' }],
    },
    {
      label: '题目分值',
      field: 'fz',
      component: 'InputNumber',
      defaultValue: 0,
      componentProps: { min: '0', max: '999999', placeholder: '请输入' },
      rules: [{ required: true, trigger: 'blur' }],
      ifShow: ({ values }) => {
        return !['Radio', 'Checkbox', 'Select'].includes(values.msmfType) && values.syfz;
      },
    },
    // {
    //   label: '使用跳题',
    //   field: 'sytt',
    //   component: 'Switch',
    //   defaultValue: true,
    //   rules: [{ required: true, trigger: 'blur' }],
    // },
  ];
  const { createMessage } = useMessage();
  const emit = defineEmits(['register', 'reload']);
  const [registerForm, { setFieldsValue, getFieldsValue, validate, resetFields, updateSchema }] = useForm({ labelWidth: 100, schemas: schemasForm });
  const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(init);
  const [registerAddForm, { openModal: openAddFormModal }] = useModal();
  async function init(data) {
    changeLoading(true);
    resetFields();
    changeLoading(false);
  }
  async function handleSubmit() {
    const values = await validate();
    if (!values) return;
    changeOkLoading(true);
    const obj = JSON.stringify({
      ...values,
      valueType: values.msmfType == 'Checkbox' ? 'array' : 'string',
    });

    const query = {
      ...values,
      msmfJson: obj,
      parentFieldId: 0,
    };
    addAloneField(query)
      .then(res => {
        createMessage.success(res.msg);
        changeOkLoading(false);
        closeModal();
        emit('reload');
      })
      .catch(() => {
        changeOkLoading(false);
      });
  }
  const dicOptions = ref<any[]>([]);
  const quesOptions = ref<any[]>([]);
  function getOptionsData(e) {
    quesOptions.value = e;
  }
  onMounted(() => {});
</script>
